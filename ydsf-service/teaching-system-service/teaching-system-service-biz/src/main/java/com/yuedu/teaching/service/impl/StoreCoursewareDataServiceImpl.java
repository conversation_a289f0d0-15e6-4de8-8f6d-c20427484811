package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.IsUseEnum;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareDataMapper;
import com.yuedu.teaching.mapper.StoreCoursewareDataStepDetailsMapper;
import com.yuedu.teaching.mapper.StoreCoursewareStepMapper;
import com.yuedu.teaching.mapper.CoursewareMapper;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.service.CoursewareStepPubService;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.dto.CoursewareCopyDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.teaching.vo.ClientStepDetailsVO;
import com.yuedu.teaching.vo.StepPubVO;
import com.yuedu.teaching.entity.StoreCoursewareData;
import com.yuedu.teaching.entity.StoreCoursewareDataStepDetails;
import com.yuedu.teaching.entity.StoreCoursewareStep;
import com.yuedu.teaching.entity.Courseware;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;


/**
 * 门店课件表服务层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Service
@RequiredArgsConstructor
public class StoreCoursewareDataServiceImpl extends ServiceImpl<StoreCoursewareDataMapper, StoreCoursewareData>
        implements StoreCoursewareDataService {

    private final CoursewareStepPubService coursewareStepPubService;
    private final StoreCoursewareDataStepDetailsMapper storeCoursewareDataStepDetailsMapper;
    private final StoreCoursewareStepMapper storeCoursewareStepMapper;
    private final CoursewareMapper coursewareMapper;


    /**
     * 创建课件副本
     *
     * @param coursewareCopyDTO 课件副本创建参数
     * @return Boolean 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createCoursewareCopy(CoursewareCopyDTO coursewareCopyDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 检查唯一性：每个账号每个课件环节只能创建一个副本
        boolean exists = this.exists(Wrappers.<StoreCoursewareData>lambdaQuery()
                .eq(StoreCoursewareData::getCoursewareId, coursewareCopyDTO.getCoursewareId())
                .eq(StoreCoursewareData::getCoursewareDataId, coursewareCopyDTO.getCoursewareDataId())
                .eq(StoreCoursewareData::getOwner, userId)
                .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                .eq(StoreCoursewareData::getSchoolId, schoolId.intValue()));

        if (exists) {
            throw new BizException("该课件环节已存在副本，不能重复创建");
        }

        // 调用CoursewareStepPubService.viewCoursewareStep获取课件数据
        CoursewareStepQuery query = new CoursewareStepQuery();
        query.setCoursewareId(coursewareCopyDTO.getCoursewareId());
        query.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());

        List<ClientStepDetailsVO> stepDetails = coursewareStepPubService.viewCoursewareStep(query);
        if (stepDetails == null || stepDetails.isEmpty()) {
            throw new BizException("未找到课件数据");
        }

        // 获取课件版本信息
        Courseware courseware = coursewareMapper.selectById(coursewareCopyDTO.getCoursewareId());
        if (courseware == null) {
            throw new BizException("课件不存在");
        }
        Integer version = ObjectUtil.defaultIfNull(courseware.getVersion(), 1);

        return createStoreCoursewareCopy(coursewareCopyDTO, stepDetails, userId, storeId.intValue(), schoolId.intValue(), version);
    }

    /**
     * 查询所有教学环节
     *
     * @param storeCoursewareDataQuery 查询参数
     * @return List<StepVO> 教学环节列表
     */
    @Override
    public List<StepVO> listStoreCoursewareSteps(StoreCoursewareDataQuery storeCoursewareDataQuery) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 查询门店课件数据
        List<StoreCoursewareStep> storeSteps = storeCoursewareStepMapper.selectList(
                Wrappers.<StoreCoursewareStep>lambdaQuery()
                        .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareDataQuery.getCoursewareId())
                        .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareDataQuery.getCoursewareDataId())
                        .eq(StoreCoursewareStep::getOwner, userId)
                        .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
                        .orderByAsc(StoreCoursewareStep::getStepOrder)
        );

        if (storeSteps.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为StepVO并构建树形结构
        return buildStepTree(storeSteps);
    }

    /**
     * 创建门店课件副本数据
     */
    private Boolean createStoreCoursewareCopy(CoursewareCopyDTO coursewareCopyDTO, List<ClientStepDetailsVO> stepDetails,
                                              Long userId, Integer storeId, Integer schoolId, Integer version) {
        try {
            // 1. 创建store_courseware_data记录
            StoreCoursewareData storeCoursewareData = new StoreCoursewareData();
            storeCoursewareData.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());
            storeCoursewareData.setVersion(version);
            storeCoursewareData.setCoursewareName(TeachingConstant.COPY_COURSE_WARE_REMAK);
            storeCoursewareData.setCoursewareId(coursewareCopyDTO.getCoursewareId());
            storeCoursewareData.setStoreId(storeId);
            storeCoursewareData.setSchoolId(schoolId);
            storeCoursewareData.setOwner(userId);
            storeCoursewareData.setIsUse(IsUseEnum.IS_USE_0.code);
            this.save(storeCoursewareData);

            // 2. 处理教学环节数据
            ClientStepDetailsVO stepDetailsVO = stepDetails.get(0);
            if (stepDetailsVO.getPages() instanceof List) {
                @SuppressWarnings("unchecked")
                List<StepPubVO> pages = (List<StepPubVO>) stepDetailsVO.getPages();

                for (StepPubVO page : pages) {
                    // 创建store_courseware_step记录
                    StoreCoursewareStep storeCoursewareStep = new StoreCoursewareStep();
                    storeCoursewareStep.setStepId(page.getId());
                    storeCoursewareStep.setCoursewareId(coursewareCopyDTO.getCoursewareId());
                    storeCoursewareStep.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());
                    // 从configs中获取stepName
                    String stepName = "";
                    if (page.getConfigs() != null) {
                        try {
                            if (page.getConfigs() instanceof String) {
                                cn.hutool.json.JSONObject configJson = cn.hutool.json.JSONUtil.parseObj((String) page.getConfigs());
                                stepName = configJson.getStr("stepName", "");
                            } else if (page.getConfigs() instanceof cn.hutool.json.JSONObject) {
                                stepName = ((cn.hutool.json.JSONObject) page.getConfigs()).getStr("stepName", "");
                            }
                        } catch (Exception e) {
                            stepName = "默认环节名称";
                        }
                    }
                    storeCoursewareStep.setStepName(stepName);
                    storeCoursewareStep.setStepParent(0); // 根据实际情况设置
                    storeCoursewareStep.setStepOrder(page.getStepOrder());
                    storeCoursewareStep.setVersion(version);
                    storeCoursewareStep.setType(2); // 页面类型
                    storeCoursewareStep.setPageTemplateId(page.getPageTemplateId());
                    storeCoursewareStep.setStoreId(storeId);
                    storeCoursewareStep.setSchoolId(schoolId);
                    storeCoursewareStep.setOwner(userId);
                    storeCoursewareStepMapper.insert(storeCoursewareStep);

                    // 创建store_courseware_data_step_details记录
                    StoreCoursewareDataStepDetails storeStepDetails = new StoreCoursewareDataStepDetails();
                    storeStepDetails.setCoursewareDataStepDetailsId(page.getId());
                    storeStepDetails.setCoursewareName(TeachingConstant.COPY_COURSE_WARE_REMAK);
                    storeStepDetails.setCoursewareId(coursewareCopyDTO.getCoursewareId());
                    storeStepDetails.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());
                    storeStepDetails.setStepId(page.getId());
                    storeStepDetails.setDetails(page.getConfigs());
                    storeStepDetails.setTool(page.getTools());
                    storeStepDetails.setPageTemplateId(page.getPageTemplateId());
                    storeStepDetails.setStoreId(storeId);
                    storeStepDetails.setSchoolId(schoolId);
                    storeStepDetails.setOwner(userId);
                    storeCoursewareDataStepDetailsMapper.insert(storeStepDetails);
                }
            }

            return true;
        } catch (Exception e) {
            throw new BizException("创建课件副本失败：" + e.getMessage());
        }
    }

    /**
     * 构建教学环节树形结构
     */
    private List<StepVO> buildStepTree(List<StoreCoursewareStep> storeSteps) {
        List<StepVO> stepVOs = storeSteps.stream()
                .map(step -> {
                    StepVO stepVO = new StepVO();
                    stepVO.setId(step.getId());
                    stepVO.setStepName(step.getStepName());
                    stepVO.setStepParent(step.getStepParent());
                    stepVO.setStepOrder(step.getStepOrder());
                    stepVO.setType(step.getType());
                    stepVO.setPageTemplateId(step.getPageTemplateId());
                    return stepVO;
                })
                .toList();

        // 构建树形结构
        List<StepVO> rootSteps = new ArrayList<>();
        for (StepVO stepVO : stepVOs) {
            if (stepVO.getStepParent().equals(0)) {
                rootSteps.add(stepVO);
            }
        }

        for (StepVO rootStep : rootSteps) {
            List<StepVO> children = getChildren(rootStep.getId(), stepVOs);
            rootStep.setChildren(children);
        }

        return rootSteps;
    }

    /**
     * 递归获取子节点
     */
    private List<StepVO> getChildren(Integer parentId, List<StepVO> allSteps) {
        List<StepVO> children = new ArrayList<>();
        for (StepVO step : allSteps) {
            if (step.getStepParent().equals(parentId)) {
                children.add(step);
            }
        }

        for (StepVO child : children) {
            List<StepVO> grandChildren = getChildren(child.getId(), allSteps);
            child.setChildren(grandChildren);
        }

        return children.isEmpty() ? Collections.emptyList() : children;
    }

}
